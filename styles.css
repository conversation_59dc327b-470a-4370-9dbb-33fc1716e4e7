/* Modern POS Theme System - 2024 Standards */
:root {
    /* Light Mode - Default */
    --bg-primary: #f8fafc;
    --bg-secondary: #ffffff;
    --bg-accent: #f1f5f9;
    --text-primary: #0f172a;
    --text-secondary: #334155;
    --text-muted: #64748b;
    --border-color: #e2e8f0;
    --accent-primary: #0ea5e9;
    --accent-secondary: #0284c7;
    --accent-gradient: linear-gradient(135deg, #0ea5e9, #0284c7);
    --shadow-color: rgba(14, 165, 233, 0.1);
    --border-radius: 8px;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --error-color: #ef4444;
    /* Brand Customization */
    --brand-primary: var(--accent-primary);
    --brand-secondary: var(--accent-secondary);
}

/* Dark Mode - Modern AI Interface (Keep Intact) */
.theme-dark {
    --bg-primary: #0f172a;
    --bg-secondary: #1e293b;
    --bg-accent: #334155;
    --text-primary: #ffffff;
    --text-secondary: #e2e8f0;
    --text-muted: #cbd5e1;
    --border-color: #475569;
    --accent-primary: #06b6d4;
    --accent-secondary: #0891b2;
    --accent-gradient: linear-gradient(135deg, #06b6d4, #0891b2);
    --shadow-color: rgba(6, 182, 212, 0.2);
    --border-radius: 12px;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --error-color: #f87171;
    --brand-primary: var(--accent-primary);
    --brand-secondary: var(--accent-secondary);
}

/* High Contrast - Accessibility Mode */
.theme-contrast {
    --bg-primary: #ffffff;
    --bg-secondary: #ffffff;
    --bg-accent: #f5f5f5;
    --text-primary: #000000;
    --text-secondary: #000000;
    --text-muted: #333333;
    --border-color: #000000;
    --accent-primary: #0066cc;
    --accent-secondary: #004499;
    --accent-gradient: linear-gradient(135deg, #0066cc, #004499);
    --shadow-color: rgba(0, 102, 204, 0.3);
    --border-radius: 4px;
    --success-color: #008000;
    --warning-color: #ff8c00;
    --error-color: #cc0000;
    --brand-primary: var(--accent-primary);
    --brand-secondary: var(--accent-secondary);
}

/* High Contrast Button Styling */
.theme-contrast .theme-contrast-button:disabled {
    background-color: #cccccc !important;
    color: #000000 !important;
    border: 2px solid #000000 !important;
}

.theme-contrast #checkoutBtn:not(:disabled) {
    background-color: var(--accent-primary) !important;
    color: #ffffff !important;
    border: 2px solid #000000 !important;
}

.theme-contrast #checkoutBtn:not(:disabled):hover {
    background-color: var(--accent-secondary) !important;
    border-color: #000000 !important;
}

/* Business Theme CSS Variables */
/* Café Theme */
.theme-cafe {
    --bg-primary: #faf7f2;
    --bg-secondary: #ffffff;
    --bg-accent: #f4f1ec;
    --text-primary: #3d2914;
    --text-secondary: #6b4e24;
    --text-muted: #8b6914;
    --border-color: #e6d5b7;
    --accent-primary: #8b4513;
    --accent-secondary: #d2691e;
    --accent-gradient: linear-gradient(45deg, #8b4513, #d2691e);
    --shadow-color: rgba(139, 69, 19, 0.1);
    --border-radius: 12px;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --error-color: #ef4444;
    --brand-primary: var(--accent-primary);
    --brand-secondary: var(--accent-secondary);
}

/* Corporate Theme */
.theme-corporate {
    --bg-primary: #f8fafc;
    --bg-secondary: #ffffff;
    --bg-accent: #f1f5f9;
    --text-primary: #1e293b;
    --text-secondary: #475569;
    --text-muted: #64748b;
    --border-color: #e2e8f0;
    --accent-primary: #3b82f6;
    --accent-secondary: #1d4ed8;
    --accent-gradient: linear-gradient(45deg, #3b82f6, #1d4ed8);
    --shadow-color: rgba(59, 130, 246, 0.1);
    --border-radius: 6px;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --error-color: #ef4444;
    --brand-primary: var(--accent-primary);
    --brand-secondary: var(--accent-secondary);
}

/* Restaurant Theme */
.theme-restaurant {
    --bg-primary: #fef2f2;
    --bg-secondary: #ffffff;
    --bg-accent: #fee2e2;
    --text-primary: #7f1d1d;
    --text-secondary: #991b1b;
    --text-muted: #b91c1c;
    --border-color: #fecaca;
    --accent-primary: #dc2626;
    --accent-secondary: #ea580c;
    --accent-gradient: linear-gradient(45deg, #dc2626, #ea580c);
    --shadow-color: rgba(220, 38, 38, 0.1);
    --border-radius: 8px;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --error-color: #ef4444;
    --brand-primary: var(--accent-primary);
    --brand-secondary: var(--accent-secondary);
}

/* Health/Organic Theme */
.theme-health {
    --bg-primary: #f0fdf4;
    --bg-secondary: #ffffff;
    --bg-accent: #dcfce7;
    --text-primary: #14532d;
    --text-secondary: #166534;
    --text-muted: #15803d;
    --border-color: #bbf7d0;
    --accent-primary: #16a34a;
    --accent-secondary: #059669;
    --accent-gradient: linear-gradient(45deg, #16a34a, #059669);
    --shadow-color: rgba(22, 163, 74, 0.1);
    --border-radius: 16px;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --error-color: #ef4444;
    --brand-primary: var(--accent-primary);
    --brand-secondary: var(--accent-secondary);
}


/* Theme Transitions */
* {
    transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
}

/* Mobile-First Responsive Design */

/* Enhanced touch targets for POS environment - larger targets for faster interaction */
.touch-target {
    min-height: 60px; /* Increased from 44px for POS use */
    min-width: 60px;
    touch-action: manipulation; /* Improves touch responsiveness */
    user-select: none; /* Prevents text selection on double-tap */
}

/* Mobile-optimized interface button */
.interface-button {
    min-width: 120px; /* Larger mobile width for better touch */
    min-height: 44px; /* Minimum touch target */
    transition: width 0.4s cubic-bezier(0.4, 0, 0.2, 1), all 0.3s ease;
}

@media (min-width: 640px) {
    .interface-button {
        min-width: 12rem; /* 192px - desktop width */
    }
}

/* Mobile-specific improvements */
@media (max-width: 639px) {
    /* Better mobile spacing */
    .mobile-spacing {
        padding: 1rem 0.75rem;
    }

    /* Larger mobile text */
    .mobile-text {
        font-size: 0.875rem;
        line-height: 1.5;
    }

    /* Mobile-friendly product grid */
    .mobile-product-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }


    /* Mobile input improvements */
    .mobile-input {
        padding: 0.875rem 1rem;
        font-size: 1rem;
    }

    /* Mobile button improvements */
    .mobile-button {
        padding: 0.875rem 1.25rem;
        font-size: 0.875rem;
        min-height: 44px;
    }
}

/* Enhanced Product Card Styling for POS Environment */
.product-card {
    min-height: 100px; /* Increased height to accommodate two-line product names */
    height: 100px; /* Fixed height to ensure consistency across all categories */
    padding: 0.5rem 0.75rem; /* Adjusted padding for better text spacing */
    border-radius: var(--border-radius);
    background-color: var(--bg-accent);
    border: 2px solid transparent;
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
    position: relative;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    /* Prevent any blur or opacity effects on siblings */
    filter: none !important;
    opacity: 1 !important;
}

/* Perfectly Consistent POS Category Tab Styling */
.category-tab {
    /* Remove all variable padding and spacing */
    padding: 0 !important;
    margin: 0 !important;
    border: 1px solid var(--border-color) !important;
    border-radius: 6px !important;
    font-size: 0.75rem !important; /* 12px */
    font-weight: 500 !important;
    transition: all 0.2s ease !important;
    cursor: pointer !important;
    /* EXACT fixed dimensions - no deviation allowed */
    height: 36px !important;
    width: 100px !important;
    min-width: 100px !important;
    max-width: 100px !important;
    min-height: 36px !important;
    max-height: 36px !important;
    /* Perfect centering and layout */
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    position: relative !important;
    overflow: hidden !important;
    white-space: nowrap !important;
    background-color: var(--bg-accent) !important;
    color: var(--text-primary) !important;
    box-sizing: border-box !important;
    flex-shrink: 0 !important; /* Prevent any flex changes */
    flex-grow: 0 !important; /* Prevent any flex changes */
    text-overflow: ellipsis !important; /* Handle text overflow */
    /* Add touch properties directly to category buttons */
    touch-action: manipulation !important;
    user-select: none !important;
}

/* Override any touch-target class interference specifically for category tabs */
.category-tab.touch-target {
    height: 36px !important;
    width: 100px !important;
    min-width: 100px !important;
    max-width: 100px !important;
    min-height: 36px !important;
    max-height: 36px !important;
}

/* Minimal POS Hover and Active States */
.category-tab:hover {
    background-color: var(--accent-primary) !important;
    color: white !important;
    border-color: var(--accent-primary) !important;
    transform: translateY(-1px) !important; /* Subtle lift effect */
    /* Maintain exact dimensions on hover */
    height: 36px !important;
    width: 100px !important;
}

.category-tab.active {
    background-color: var(--accent-primary) !important;
    color: white !important;
    border-color: var(--accent-primary) !important;
    /* Remove fixed dimensions - inherit from base category-tab styles */
}

/* Consistent icon styling */
.category-tab i {
    margin-right: 0.25rem !important; /* 4px spacing - tighter for compact design */
    font-size: 0.75rem !important; /* Same size as text for consistency */
    transition: opacity 0.2s ease !important;
    flex-shrink: 0 !important; /* Prevent icon from shrinking */
    width: 12px !important; /* Fixed width for alignment */
    text-align: center !important; /* Center icons within their space */
    display: inline-block !important; /* Ensure proper icon display */
}

/* Simple active feedback */
.category-tab:active {
    transform: translateY(0) !important; /* Remove lift on press */
    opacity: 0.9 !important;
    /* Remove fixed dimensions - inherit from base category-tab styles */
}

/* Responsive Category Navigation System */
.category-tabs-wrapper {
    display: flex !important;
    align-items: center !important;
    justify-content: flex-start !important;
    gap: 0.25rem !important;
    width: 100% !important;
    position: relative !important;
    min-height: 44px !important; /* Ensure consistent wrapper height */
    padding: 0 !important; /* Remove any padding that could cause misalignment */
    margin: 0 !important; /* Remove any margin that could cause misalignment */
}

.category-tabs-container {
    display: flex !important;
    flex-wrap: nowrap !important;
    align-items: center !important;
    justify-content: flex-start !important;
    overflow: hidden !important;
    gap: 0.25rem !important;
    flex: 1 !important;
    min-width: 0 !important;
    position: relative !important;
    scroll-behavior: smooth !important;
    min-height: 44px !important; /* Match wrapper height */
    padding: 0 !important; /* Remove any padding that could cause misalignment */
    margin: 0 !important; /* Remove any margin that could cause misalignment */
    /* Add fade effect at edges */
    mask-image: linear-gradient(to right, transparent 0px, black 8px, black calc(100% - 8px), transparent 100%) !important;
    -webkit-mask-image: linear-gradient(to right, transparent 0px, black 8px, black calc(100% - 8px), transparent 100%) !important;
}

/* Category tab enhanced for better touch targets */
.category-tab {
    /* POS-optimized touch targets - 44dp minimum */
    height: 44px !important;
    min-height: 44px !important;
    max-height: 44px !important;
    min-width: 80px !important;
    padding: 0 12px !important;
    margin: 0 !important;
    border: 1px solid var(--border-color) !important;
    border-radius: 8px !important;
    font-size: 0.75rem !important;
    font-weight: 500 !important;
    transition: all 0.2s ease !important;
    cursor: pointer !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    position: relative !important;
    overflow: hidden !important;
    white-space: nowrap !important;
    background-color: var(--bg-accent) !important;
    color: var(--text-primary) !important;
    box-sizing: border-box !important;
    flex-shrink: 0 !important;
    flex-grow: 0 !important;
    text-overflow: ellipsis !important;
    touch-action: manipulation !important;
    user-select: none !important;
    outline: none !important;
    align-self: center !important;
    vertical-align: middle !important;
}

.category-tab:focus-visible {
    outline: 2px solid var(--accent-primary) !important;
    outline-offset: 2px !important;
}

.category-tab span {
    display: block !important;
    text-overflow: ellipsis !important;
    overflow: hidden !important;
    white-space: nowrap !important;
}

/* Scroll arrows for horizontal navigation */
.category-scroll-btn {
    /* Match category-tab dimensions exactly for consistent UI */
    height: 44px !important;
    min-height: 44px !important;
    max-height: 44px !important;
    width: 44px !important;
    min-width: 44px !important;
    margin: 0 !important;
    padding: 0 !important;
    border: 2px solid var(--border-color) !important; /* Thicker border for better visibility */
    border-radius: 8px !important;
    background-color: var(--bg-secondary) !important;
    color: var(--text-primary) !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    cursor: pointer !important;
    transition: all 0.2s ease !important;
    font-size: 0.875rem !important; /* Slightly larger icon */
    font-weight: 600 !important; /* Bold icons for better visibility */
    z-index: 2 !important;
    flex-shrink: 0 !important;
    flex-grow: 0 !important;
    box-sizing: border-box !important;
    align-self: center !important;
    vertical-align: middle !important; /* Ensure vertical alignment */
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important; /* Subtle shadow for depth */
}

.category-scroll-btn:hover {
    background-color: var(--accent-primary) !important;
    color: white !important;
    border-color: var(--accent-primary) !important;
    transform: translateY(-1px) !important; /* Slight lift on hover */
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15) !important;
}

.category-scroll-btn:active {
    transform: translateY(0) !important;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
}

.category-scroll-btn:disabled {
    opacity: 0.5 !important;
    cursor: not-allowed !important;
    transform: none !important;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
}

/* Overflow button */
.category-overflow-container {
    position: relative !important;
    flex-shrink: 0 !important;
}

.category-overflow-btn {
    height: 44px !important;
    width: 44px !important;
    min-width: 44px !important;
    min-height: 44px !important;
    border: 1px solid var(--border-color) !important;
    border-radius: 8px !important;
    background-color: var(--bg-accent) !important;
    color: var(--text-primary) !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    cursor: pointer !important;
    transition: all 0.2s ease !important;
    font-size: 0.875rem !important;
    outline: none !important;
}

.category-overflow-btn:focus-visible {
    outline: 2px solid var(--accent-primary) !important;
    outline-offset: 2px !important;
}

.category-overflow-btn:hover {
    background-color: var(--accent-primary) !important;
    color: white !important;
    border-color: var(--accent-primary) !important;
}

/* Desktop overflow menu */
.category-overflow-menu {
    position: absolute !important;
    top: 100% !important;
    right: 0 !important;
    margin-top: 0.5rem !important;
    background-color: var(--bg-secondary) !important;
    border: 1px solid var(--border-color) !important;
    border-radius: 8px !important;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12) !important;
    z-index: 50 !important;
    min-width: 180px !important;
    max-height: 300px !important;
    overflow-y: auto !important;
    padding: 0.5rem !important;
}

.category-overflow-menu .category-tab {
    width: 100% !important;
    max-width: none !important;
    min-width: auto !important;
    justify-content: flex-start !important;
    margin-bottom: 0.25rem !important;
    border-radius: 6px !important;
    height: 40px !important;
    min-height: 40px !important;
    max-height: 40px !important;
    padding: 0 12px !important;
}

.category-overflow-menu .category-tab:last-child {
    margin-bottom: 0 !important;
}

/* Full-screen modal for mobile */
.category-modal {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    z-index: 1000 !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    padding: 1rem !important;
}

.category-modal-overlay {
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    background-color: rgba(0, 0, 0, 0.5) !important;
    backdrop-filter: blur(4px) !important;
}

.category-modal-content {
    position: relative !important;
    background-color: var(--bg-secondary) !important;
    border-radius: 12px !important;
    max-width: 90vw !important;
    max-height: 85vh !important;
    width: 100% !important;
    overflow: hidden !important;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15) !important;
}

.category-modal-header {
    display: flex !important;
    align-items: center !important;
    justify-content: space-between !important;
    padding: 1.25rem 1.5rem 1rem !important;
    border-bottom: 1px solid var(--border-color) !important;
}

.category-modal-header h3 {
    font-size: 1.125rem !important;
    font-weight: 600 !important;
    color: var(--text-primary) !important;
    margin: 0 !important;
}

.category-modal-close {
    height: 40px !important;
    width: 40px !important;
    border: none !important;
    background: none !important;
    color: var(--text-muted) !important;
    cursor: pointer !important;
    border-radius: 8px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    font-size: 1.125rem !important;
    transition: all 0.2s ease !important;
}

.category-modal-close:hover {
    background-color: var(--bg-accent) !important;
    color: var(--text-primary) !important;
}

.category-modal-grid {
    padding: 1.5rem !important;
    display: grid !important;
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr)) !important;
    gap: 0.75rem !important;
    max-height: 60vh !important;
    overflow-y: auto !important;
}

.category-modal-grid .category-tab {
    height: 80px !important;
    min-height: 80px !important;
    flex-direction: column !important;
    text-align: center !important;
    gap: 0.5rem !important;
    padding: 0.75rem !important;
    font-size: 0.875rem !important;
}

.category-modal-grid .category-tab i {
    font-size: 1.5rem !important;
    margin-right: 0 !important;
    width: auto !important;
}

/* Responsive breakpoints */
@media (max-width: 640px) {
    .category-tabs-container {
        gap: 0.125rem !important;
    }
    
    .category-tab {
        min-width: 70px !important;
        padding: 0 8px !important;
        font-size: 0.6875rem !important;
        height: 40px !important;
        min-height: 40px !important;
        max-height: 40px !important;
    }
    
    .category-scroll-btn {
        width: 40px !important;
        min-width: 40px !important;
        height: 40px !important;
        min-height: 40px !important;
        max-height: 40px !important;
        font-size: 0.75rem !important;
        border-width: 2px !important; /* Maintain visible border */
    }
    
    .category-overflow-btn {
        height: 40px !important;
        min-height: 40px !important;
        width: 40px !important;
        min-width: 40px !important;
    }
    
    /* Use modal instead of dropdown on mobile */
    .category-overflow-menu {
        display: none !important;
    }
}

@media (min-width: 641px) and (max-width: 767px) {
    .category-tab {
        min-width: 75px !important;
        font-size: 0.75rem !important;
        height: 44px !important;
        min-height: 44px !important;
        max-height: 44px !important;
    }

    .category-scroll-btn {
        width: 44px !important;
        min-width: 44px !important;
        height: 44px !important;
        min-height: 44px !important;
        max-height: 44px !important;
        font-size: 0.875rem !important;
        border-width: 2px !important;
    }

    .category-modal {
        display: none !important;
    }
}

@media (min-width: 1025px) {
    .category-tab {
        min-width: 90px !important;
        font-size: 0.875rem !important;
    }
    
    .category-scroll-btn {
        width: 44px !important;
        min-width: 44px !important;
        height: 44px !important;
        min-height: 44px !important;
        max-height: 44px !important;
        font-size: 0.875rem !important;
        border-width: 2px !important; /* Maintain visible border */
    }
    
    .category-modal {
        display: none !important;
    }
}


.product-card:hover {
    transform: translateY(-2px);
    border-color: var(--accent-primary);
    box-shadow: 0 4px 12px var(--shadow-color);
}

.product-card:active {
    transform: scale(0.98);
    box-shadow: 0 2px 6px var(--shadow-color);
}

/* Ensure consistent content alignment within product cards */
.product-card .text-center {
    width: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    height: 100%;
}

/* Ensure consistent icon sizing across all categories */
.product-card i {
    flex-shrink: 0;
}

/* Ensure text doesn't overflow and maintains consistent spacing */
.product-card h3 {
    margin: 0;
    line-height: 1.2;
    height: 2.4em; /* Fixed height for 2 lines to ensure consistent price positioning */
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    overflow: hidden;
    word-wrap: break-word;
    hyphens: auto;
    white-space: normal;
    font-size: 0.75rem !important; /* Override Tailwind classes with smaller text */
}

.product-card p {
    margin: 0;
    line-height: 1.2;
    font-size: 0.75rem !important; /* Override Tailwind classes with smaller text */
}

/* Improve spacing in product grid for POS use */
#productContainer {
    gap: 1rem; /* Increased gap between products */
    max-height: 330px; /* Limit to approximately 3 rows (100px + 1rem gap per row) */
    overflow-y: auto;
    padding-right: 8px; /* Add space for scrollbar */
    padding-top: 4px; /* Add top padding to prevent hover transform clipping */
}

/* Ensure no hover effects affect siblings */
#productContainer .product-card:not(:hover) {
    filter: none !important;
    opacity: 1 !important;
}

/* Custom scrollbar for product grid */
#productContainer::-webkit-scrollbar {
    width: 6px;
}

#productContainer::-webkit-scrollbar-track {
    background: var(--bg-accent);
    border-radius: 3px;
}

#productContainer::-webkit-scrollbar-thumb {
    background: var(--accent-primary);
    border-radius: 3px;
}

#productContainer::-webkit-scrollbar-thumb:hover {
    background: var(--accent-secondary);
}

@media (min-width: 768px) {
    #productContainer {
        gap: 1.5rem; /* Even more space on larger screens */
        max-height: 375px; /* Adjust for larger gap on desktop (100px + 1.5rem gap per row) */
        padding-top: 6px; /* Slightly more padding on larger screens for hover effects */
    }

    .product-card {
        min-height: 100px; /* Increased height for better visual balance on desktop */
        height: 100px; /* Fixed height to ensure consistency across all categories */
        padding: 0.5rem 1rem;
    }
}

/* Touch-friendly interactions */
@media (hover: none) and (pointer: coarse) {
    /* Remove hover effects on touch devices */
    .product-card:hover {
        transform: none;
        border-color: var(--border-color) !important;
        box-shadow: none;
    }

    /* Add touch feedback instead */
    .product-card:active {
        transform: scale(0.98);
        background-color: var(--accent-primary);
        color: white;
    }

    .quick-action:hover {
        background-color: transparent;
        border-color: var(--border-color);
        transform: none;
    }

    .quick-action:active {
        background-color: var(--bg-accent);
        transform: scale(0.98);
    }
}

/* Component Animations */
.message-bubble {
    animation: slideIn 0.3s ease-out;
}
@keyframes slideIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}
.product-card:hover {
    transform: translateY(-2px);
    transition: transform 0.2s ease;
    border-color: var(--accent-primary) !important;
    box-shadow: 0 4px 12px var(--shadow-color);
}
.typing-indicator {
    animation: pulse 1.5s infinite;
}
.fade-in {
    animation: fadeIn 0.5s ease-in;
}
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

/* Quick Actions Enhanced Styling */
.quick-action {
    position: relative;
    overflow: hidden;
    background-color: var(--bg-secondary);
    border: 1px solid var(--border-color);
    transition: all 0.2s ease;
}
.quick-action:hover {
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    border-color: var(--accent-primary);
}
.quick-action::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
    transition: left 0.5s;
}
.quick-action:hover::before {
    left: 100%;
}
.quick-action .icon-container {
    transition: all 0.2s ease;
}
.quick-action:hover .icon-container {
    transform: scale(1.1);
}
.quick-action:active {
    transform: scale(0.98);
}

.suggestion-item:hover {
    background: var(--accent-gradient);
    color: white;
    transform: scale(1.02);
    transition: all 0.2s ease;
}
.chat-container {
    height: 350px;
    max-height: 350px;
    overflow-y: auto;
    scroll-behavior: smooth;
}

/* Mobile-responsive chat container */
@media (max-width: 650px) {
    .chat-container {
        max-height: 350px;
    }
}

/* Tablet-specific chat container - full height in conversation mode (includes iPad) */
@media (min-width: 651px) and (max-width: 1279px) {
    .chat-container {
        height: calc(100vh - 300px);
        max-height: calc(100vh - 300px);
        min-height: 450px;
    }
    
    #conversationArea {
        min-height: calc(100vh - 200px);
    }
}

/* iPad-specific optimizations */
@media (min-width: 768px) and (max-width: 1024px) {
    .chat-container {
        height: calc(100vh - 280px);
        max-height: calc(100vh - 280px);
        min-height: 500px;
    }
    
    #conversationArea {
        min-height: calc(100vh - 180px);
    }
}
.mode-toggle {
    background: var(--accent-gradient);
}
.theme-selector {
    background: var(--accent-gradient);
}

/* POS UI Improvements - Uniform Quick Actions (Fixed Width, No Scroll) */
.quick-actions-compact {
    display: flex;
    justify-content: space-between;
    gap: 1rem;
    padding: 0.25rem 0;
    width: 100%;
}

.quick-action-icon {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 0.75rem 0.25rem;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    background-color: var(--bg-secondary);
    transition: all 0.2s ease;
    cursor: pointer;
    height: 60px; /* Fixed height for all buttons */
    width: 100%; /* Ensure button fills container */
}

.quick-action-icon:hover {
    background-color: var(--bg-accent);
    border-color: var(--accent-primary);
    transform: translateY(-1px);
    box-shadow: 0 2px 8px var(--shadow-color);
}

.quick-action-icon i {
    font-size: 1.25rem;
    margin-bottom: 0.25rem;
    color: var(--accent-primary);
}

.quick-action-icon span {
    font-size: 0.7rem;
    font-weight: 500;
    color: var(--text-secondary);
    text-align: center;
    line-height: 1.1;
    white-space: nowrap;
}

/* Dropdown positioning - appear above buttons to avoid scrolling */
.quick-action-dropdown {
    position: absolute;
    bottom: 100%; /* Position above the button */
    left: 0;
    margin-bottom: 0.5rem;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    background-color: var(--bg-secondary);
    box-shadow: 0 -4px 12px var(--shadow-color);
    z-index: 1000;
    min-width: 160px;
}

/* Adaptive Current Order Container */
.order-container {
    display: flex;
    flex-direction: column;
    height: calc(100vh - 200px);
    max-height: 800px;
    min-height: 400px;
}


.order-items-list {
    flex-grow: 1;
    overflow-y: auto;
    min-height: 350px;
    padding: 0;
}

.order-actions-sticky {
    flex-shrink: 0;
    padding: 16px;
    background: var(--bg-secondary);
    border-top: 1px solid var(--border-color);
}

/* Custom scrollbar for order items */
.order-items-list::-webkit-scrollbar {
    width: 6px;
}

.order-items-list::-webkit-scrollbar-track {
    background: var(--bg-accent);
    border-radius: 3px;
}

.order-items-list::-webkit-scrollbar-thumb {
    background: var(--accent-primary);
    border-radius: 3px;
}

.order-items-list::-webkit-scrollbar-thumb:hover {
    background: var(--accent-secondary);
}

/* Scroll fade indicators */
.order-items-list {
    position: relative;
}

.order-items-list::before,
.order-items-list::after {
    content: '';
    position: absolute;
    left: 0;
    right: 0;
    height: 10px;
    z-index: 1;
    pointer-events: none;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.order-items-list::before {
    top: 0;
    background: linear-gradient(to bottom, var(--bg-secondary), transparent);
}

.order-items-list::after {
    bottom: 0;
    background: linear-gradient(to top, var(--bg-secondary), transparent);
}

.order-items-list.has-scroll::before,
.order-items-list.has-scroll::after {
    opacity: 1;
}

/* Conversation area height matching Current Order */
#conversationArea {
    min-height: 600px;
    display: flex;
    flex-direction: column;
}

#conversationArea .p-6 {
    flex: 1;
    display: flex;
    flex-direction: column;
}

#chatContainer {
    height: 350px;
    min-height: 350px;
    max-height: 350px;
    overflow-y: auto;
}

/* Responsive adjustments to maintain fixed width and uniform sizing */
@media (max-width: 768px) {
    .quick-actions-compact {
        gap: 0.75rem;
    }

    .quick-action-icon {
        padding: 0.6rem 0.2rem;
        height: 58px; /* Slightly smaller but still uniform */
    }

    .quick-action-icon i {
        font-size: 1.1rem;
    }

    .quick-action-icon span {
        font-size: 0.65rem;
    }
}

/* Tablet-specific order list height */
@media (min-width: 640px) and (max-width: 1023px) {
    .order-items-list {
        min-height: 450px;
        max-height: 600px;
    }
}

@media (max-width: 480px) {
    .quick-actions-compact {
        gap: 0.5rem;
    }

    .quick-action-icon {
        padding: 0.5rem 0.15rem;
        height: 55px; /* Smaller but still uniform */
    }

    .quick-action-icon i {
        font-size: 1rem;
        margin-bottom: 0.2rem;
    }

    .quick-action-icon span {
        font-size: 0.6rem;
    }
}


/* Mobile Tabbed Interface Styles */
/* Mobile tab navigation */
.mobile-tab {
    color: var(--text-muted);
    border-bottom: 3px solid transparent;
    touch-action: manipulation;
    min-height: 44px;
    transition: all 0.2s ease;
    position: relative;
    font-size: 0.875rem;
    font-weight: 500;
}

/* Mobile tab sizing adjustments */
@media (max-width: 767px) {
    .mobile-tab {
        padding: 0.5rem 0.75rem !important;
        font-size: 0.75rem !important;
        min-height: 36px !important;
    }
    
    .mobile-tab i {
        font-size: 0.875rem !important;
        margin-right: 0.375rem !important;
    }
    
    .mobile-tab span {
        font-size: 0.75rem !important;
    }
    
    /* Compact mobile tab navigation */
    #mobileTabNav {
        padding: 0 !important;
    }
    
    #mobileTabNav .max-w-7xl {
        padding-left: 1rem !important;
        padding-right: 1rem !important;
    }
}

@media (max-width: 767px) {
    .mobile-tab {
        padding: 0.5rem 0.75rem !important;
        font-size: 0.75rem !important;
    }
    
    .mobile-tab i {
        font-size: 0.875rem !important;
        margin-right: 0.375rem !important;
    }
    
    .mobile-tab span {
        font-size: 0.75rem !important;
    }
}

.mobile-tab.active-tab {
    color: var(--accent-primary);
    border-bottom-color: var(--accent-primary);
    background-color: var(--bg-accent);
    font-weight: 600;
}

/* Desktop hover effects for tabs */
@media (hover: hover) and (pointer: fine) {
    .mobile-tab:hover {
        color: var(--accent-primary);
        background-color: var(--bg-accent);
        transform: translateY(-1px);
    }
}

/* Touch device effects for tabs */
@media (hover: none) and (pointer: coarse) {
    .mobile-tab:active {
        color: var(--accent-primary);
        background-color: var(--bg-accent);
        transform: scale(0.98);
    }
}

/* Tab content containers */
.tab-content {
    display: none;
    opacity: 0;
    transform: translateY(10px);
    transition: all 0.3s ease;
}

.tab-content.active-content {
    display: block;
    opacity: 1;
    transform: translateY(0);
}

/* Animation for tab content appearing */
@keyframes tabContentFadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.tab-content.active-content {
    animation: tabContentFadeIn 0.3s ease forwards;
}

/* Badge styling */
.badge {
    font-size: 0.625rem;
    font-weight: 600;
    line-height: 1;
    min-width: 1.25rem;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

/* Mobile tab navigation and content visibility */
@media (max-width: 767px) {
    /* Compact header on mobile */
    header .h-16 {
        height: 2.5rem !important; /* 40px instead of 64px */
    }
    
    header h1 {
        font-size: 1.125rem !important; /* Smaller logo text */
    }
    
    header .space-x-3 {
        gap: 0.5rem !important;
    }
    
    #themeToggle {
        padding: 0.375rem !important;
        min-height: 32px !important;
        min-width: 32px !important;
        font-size: 0.75rem !important;
    }
    
    #themeToggle span {
        display: none !important; /* Hide "Light Mode" text on mobile */
    }
    
    #mobileTabNav {
        display: block !important;
    }
    
    /* Hide desktop layout on mobile */
    #desktopLayout {
        display: none !important;
    }
    
    /* Show mobile tab content */
    #mobileTabContent {
        display: block !important;
    }
    
    /* Ensure mobile content appears in tab containers */
    #productsTabContent.active-content {
        display: block !important;
    }
    
    #orderTabContent.active-content {
        display: block !important;
    }
    
    /* Mobile-specific content sizing */
    .max-w-7xl {
        padding-left: 1rem;
        padding-right: 1rem;
        padding-top: 1rem;
        padding-bottom: 1rem;
    }
    
    /* Compact product grid for mobile */
    .product-grid {
        gap: 0.625rem !important;
        grid-template-columns: repeat(4, 1fr) !important; /* 4 columns on mobile */
    }
    
    .product-card {
        padding: 0.5rem 0.375rem !important;
        min-height: 65px !important;
        height: 65px !important;
        display: flex !important;
        flex-direction: column !important;
        justify-content: center !important;
        align-items: center !important;
        text-align: center !important;
    }
    
    .product-card h3 {
        font-size: 0.6875rem !important;
        margin-bottom: 0.125rem !important;
        line-height: 1.2 !important;
        font-weight: 500 !important;
    }
    
    .product-card p {
        font-size: 0.6875rem !important;
        font-weight: 600 !important;
        margin: 0 !important;
    }
    
    .product-card i {
        font-size: 1rem !important;
        margin-bottom: 0.25rem !important;
    }
    
    .product-card .text-center {
        display: flex !important;
        flex-direction: column !important;
        justify-content: center !important;
        align-items: center !important;
        height: 100% !important;
    }
    
    /* Compact section headers */
    h2 {
        font-size: 1rem !important;
        margin-bottom: 0.75rem !important;
    }
    
    h3 {
        font-size: 0.875rem !important;
        margin-bottom: 0.5rem !important;
    }
    
    /* Compact padding for sections - much smaller */
    .shadow-sm.border {
        margin-bottom: 0.5rem !important;
    }
    
    .shadow-sm.border .p-6 {
        padding: 0.5rem !important;
    }
    
    .shadow-sm.border .p-4 {
        padding: 0.375rem !important;
    }
    
    /* Extra compact 'Quick Select Products' section */
    section[id*="productSelection"] .p-6,
    section:has(h2:contains("Quick Select Products")) .p-6,
    .product-selection-section .p-6 {
        padding: 0.375rem !important;
        margin-bottom: 0.25rem !important;
    }
    
    /* Section headers much smaller */
    section h2 {
        margin-bottom: 0.375rem !important;
        padding-bottom: 0.25rem !important;
    }
    
    /* Chat container mobile optimization */
    .chat-container {
        padding: 0.75rem !important;
        max-height: 200px !important;
        min-height: 150px !important;
    }
    
    .message-bubble {
        margin-bottom: 0.75rem !important;
        padding: 0.5rem !important;
        font-size: 0.8rem !important;
    }
    
    /* Input areas - mobile responsive */
    input[type="text"] {
        padding: 0.75rem !important;
        font-size: 0.875rem !important;
        min-height: 44px !important;
        border-radius: 8px !important;
    }
    
    /* Button styling - mobile responsive */
    button {
        padding: 0.75rem 1rem !important;
        font-size: 0.875rem !important;
        min-height: 44px !important;
        border-radius: 8px !important;
        font-weight: 500 !important;
    }
    
    /* Specific button types */
    .touch-target {
        min-height: 44px !important;
        min-width: 44px !important;
        padding: 0.75rem !important;
    }
    
    /* Primary buttons (checkout, send, etc.) */
    button[style*="--success-color"], 
    button[style*="--accent-primary"],
    #checkoutBtn,
    #sendBtn {
        padding: 1rem 1.5rem !important;
        font-size: 0.875rem !important;
        font-weight: 600 !important;
        min-height: 48px !important;
    }
    
    /* Secondary buttons */
    .theme-option,
    .quick-action-icon {
        padding: 0.75rem !important;
        font-size: 0.8rem !important;
        min-height: 44px !important;
    }
    
    /* Form elements */
    select {
        padding: 0.75rem !important;
        font-size: 0.875rem !important;
        min-height: 44px !important;
        border-radius: 8px !important;
    }
    
    /* Dropdown menus */
    .dropdown-menu,
    #themeDropdown,
    #discountDropdown,
    #customerDropdown {
        font-size: 0.875rem !important;
    }
    
    .dropdown-menu button,
    .theme-option,
    .discount-option,
    .customer-quick {
        padding: 0.75rem 1rem !important;
        min-height: 44px !important;
        font-size: 0.875rem !important;
    }
    
    /* Order items mobile optimization */
    .order-item-card {
        padding: 0.75rem !important;
        margin-bottom: 0.5rem !important;
    }
    
    .order-item-header {
        margin-bottom: 0.5rem !important;
    }
    
    .product-name {
        font-size: 0.875rem !important;
        font-weight: 500 !important;
    }
    
    .product-unit-price {
        font-size: 0.75rem !important;
    }
    
    .item-total-price {
        font-size: 0.875rem !important;
        font-weight: 600 !important;
    }
    
    /* Category tabs mobile - single line, very compact */
    .category-tabs-container {
        display: flex !important;
        flex-wrap: nowrap !important;
        align-items: center !important;
        overflow-x: auto !important;
        gap: 0.25rem !important;
        padding: 0 !important; /* Remove vertical padding to fix alignment */
        margin: 0 !important; /* Remove margin to fix alignment */
        scrollbar-width: none !important;
        -ms-overflow-style: none !important;
        /* Remove fade effect on mobile for better visibility */
        mask-image: none !important;
        -webkit-mask-image: none !important;
    }
    
    .category-tabs-container::-webkit-scrollbar {
        display: none !important;
    }
    
    .category-tab {
        padding: 0.25rem 0.375rem !important;
        font-size: 0.625rem !important;
        margin: 0 !important;
        min-height: 28px !important;
        height: 28px !important;
        max-height: 28px !important;
        border-radius: 4px !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        white-space: nowrap !important;
        flex-shrink: 0 !important;
        min-width: fit-content !important;
        box-sizing: border-box !important;
        align-self: center !important;
        vertical-align: middle !important;
    }
    
    /* Ensure active state maintains height on mobile */
    .category-tab.active {
        height: 28px !important;
        min-height: 28px !important;
        max-height: 28px !important;
    }
    
    .category-tab i {
        font-size: 0.625rem !important;
        margin-right: 0.1875rem !important;
    }
    
    /* Mobile scroll buttons - match category tab height exactly */
    .category-scroll-btn {
        height: 28px !important;
        min-height: 28px !important;
        max-height: 28px !important;
        width: 28px !important;
        min-width: 28px !important;
        font-size: 0.625rem !important;
        border-width: 1px !important; /* Thinner border on small screens */
        margin: 0 !important;
        padding: 0 !important;
        border-radius: 4px !important;
        box-sizing: border-box !important;
        align-self: center !important;
        vertical-align: middle !important;
    }
    
    /* Mobile category tabs wrapper */
    .category-tabs-wrapper {
        gap: 0.125rem !important;
    }
}

/* Tablet-specific adjustments (768px - 1024px) */
@media (min-width: 768px) and (max-width: 1024px) {
    /* Tablet product grid */
    .product-grid {
        gap: 0.75rem !important;
        grid-template-columns: repeat(5, 1fr) !important; /* 5 columns on tablet for better space usage */
    }
    
    /* Tablet sizing for product cards */
    .product-card {
        min-height: 75px !important;
        height: 75px !important;
        padding: 0.625rem 0.5rem !important;
        display: flex !important;
        flex-direction: column !important;
        justify-content: center !important;
        align-items: center !important;
        text-align: center !important;
    }
    
    .product-card h3 {
        font-size: 0.75rem !important;
        margin-bottom: 0.25rem !important;
        line-height: 1.2 !important;
        font-weight: 500 !important;
    }
    
    .product-card p {
        font-size: 0.75rem !important;
        font-weight: 600 !important;
        margin: 0 !important;
    }
    
    .product-card i {
        font-size: 1.125rem !important;
        margin-bottom: 0.375rem !important;
    }
    
    .product-card .text-center {
        display: flex !important;
        flex-direction: column !important;
        justify-content: center !important;
        align-items: center !important;
        height: 100% !important;
    }
    
    /* Category tabs on tablet - single line, compact */
    .category-tabs-container {
        display: flex !important;
        flex-wrap: nowrap !important;
        align-items: center !important;
        overflow-x: auto !important;
        gap: 0.375rem !important;
        padding: 0 !important; /* Remove vertical padding to fix alignment */
        margin: 0 !important; /* Remove margin to fix alignment */
        scrollbar-width: none !important;
        -ms-overflow-style: none !important;
        /* Remove fade effect on tablet for better visibility */
        mask-image: none !important;
        -webkit-mask-image: none !important;
    }
    
    .category-tabs-container::-webkit-scrollbar {
        display: none !important;
    }
    
    .category-tab {
        padding: 0.375rem 0.5rem !important;
        font-size: 0.6875rem !important;
        margin: 0 !important;
        min-height: 32px !important;
        height: 32px !important;
        max-height: 32px !important;
        border-radius: 6px !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        white-space: nowrap !important;
        flex-shrink: 0 !important;
        min-width: fit-content !important;
        box-sizing: border-box !important;
        align-self: center !important;
        vertical-align: middle !important;
    }
    
    /* Ensure active state maintains height on tablet */
    .category-tab.active {
        height: 32px !important;
        min-height: 32px !important;
        max-height: 32px !important;
    }
    
    .category-tab i {
        font-size: 0.6875rem !important;
        margin-right: 0.25rem !important;
    }
    
    /* Tablet scroll buttons - match category tab height exactly */
    .category-scroll-btn {
        height: 32px !important;
        min-height: 32px !important;
        max-height: 32px !important;
        width: 32px !important;
        min-width: 32px !important;
        font-size: 0.6875rem !important;
        border-width: 2px !important; /* Maintain visible border */
        margin: 0 !important;
        padding: 0 !important;
        border-radius: 6px !important;
        box-sizing: border-box !important;
        align-self: center !important;
        vertical-align: middle !important;
    }
    
    /* Tablet category tabs wrapper */
    .category-tabs-wrapper {
        gap: 0.25rem !important;
    }
    
    h2 {
        font-size: 1.125rem !important;
    }
    
    /* Tablet form and button optimization */
    input[type="text"] {
        min-height: 48px !important;
        padding: 0.875rem 1rem !important;
        font-size: 0.9375rem !important;
        border-radius: 10px !important;
    }
    
    button {
        min-height: 48px !important;
        padding: 0.875rem 1.25rem !important;
        font-size: 0.9375rem !important;
        border-radius: 10px !important;
        font-weight: 500 !important;
    }
    
    /* Primary buttons on tablet */
    #checkoutBtn,
    #sendBtn,
    button[style*="--success-color"], 
    button[style*="--accent-primary"] {
        padding: 1.125rem 2rem !important;
        font-size: 1rem !important;
        font-weight: 600 !important;
        min-height: 52px !important;
    }
    
    .touch-target {
        min-height: 48px !important;
        min-width: 48px !important;
        padding: 0.875rem !important;
    }
    
    /* Category tabs on tablet */
    .category-tab {
        padding: 0.75rem 1rem !important;
        font-size: 0.875rem !important;
        min-height: 44px !important;
    }
    
    /* Order controls on tablet */
    .quantity-btn {
        min-height: 44px !important;
        min-width: 44px !important;
        padding: 0.75rem !important;
    }
    
    .remove-item-btn {
        min-height: 40px !important;
        min-width: 40px !important;
        padding: 0.5rem !important;
    }
    
    .chat-container {
        max-height: 250px !important;
        min-height: 180px !important;
    }
}

/* Phone-specific adjustments (below 768px) */
@media (max-width: 767px) {
    /* Extra compact for phones */
    .max-w-7xl {
        padding-left: 0.75rem;
        padding-right: 0.75rem;
        padding-top: 0.75rem;
        padding-bottom: 0.75rem;
    }
    
    .product-grid {
        grid-template-columns: repeat(4, 1fr) !important; /* 4 columns on phones for compact cards */
        gap: 0.375rem !important;
    }
    
    .product-card {
        min-height: 58px !important;
        height: 58px !important;
        padding: 0.375rem 0.25rem !important;
        display: flex !important;
        flex-direction: column !important;
        justify-content: center !important;
        align-items: center !important;
        text-align: center !important;
    }
    
    .product-card h3 {
        font-size: 0.625rem !important;
        margin-bottom: 0.0625rem !important;
        line-height: 1.1 !important;
        font-weight: 500 !important;
    }
    
    .product-card p {
        font-size: 0.625rem !important;
        font-weight: 600 !important;
        margin: 0 !important;
    }
    
    .product-card i {
        font-size: 0.875rem !important;
        margin-bottom: 0.125rem !important;
    }
    
    .product-card .text-center {
        display: flex !important;
        flex-direction: column !important;
        justify-content: center !important;
        align-items: center !important;
        height: 100% !important;
    }
    
    .shadow-sm.border .p-6 {
        padding: 0.75rem !important;
    }
    
    .shadow-sm.border .p-4 {
        padding: 0.5rem !important;
    }
    
    .chat-container {
        max-height: 150px !important;
        min-height: 120px !important;
        padding: 0.5rem !important;
    }
    
    .message-bubble {
        font-size: 0.75rem !important;
        padding: 0.375rem !important;
    }
    
    /* Phone form and button optimization */
    input[type="text"] {
        min-height: 40px !important;
        padding: 0.625rem 0.75rem !important;
        font-size: 0.8125rem !important;
        border-radius: 6px !important;
    }
    
    button {
        min-height: 40px !important;
        padding: 0.625rem 0.875rem !important;
        font-size: 0.8125rem !important;
        border-radius: 6px !important;
        font-weight: 500 !important;
    }
    
    /* Primary buttons on phone */
    #checkoutBtn,
    #sendBtn,
    button[style*="--success-color"], 
    button[style*="--accent-primary"] {
        padding: 0.875rem 1.25rem !important;
        font-size: 0.875rem !important;
        font-weight: 600 !important;
        min-height: 44px !important;
    }
    
    .touch-target {
        min-height: 40px !important;
        min-width: 40px !important;
        padding: 0.625rem !important;
    }
    
    /* Compact order controls on phone */
    .quantity-btn {
        min-height: 36px !important;
        min-width: 36px !important;
        padding: 0.5rem !important;
        font-size: 0.75rem !important;
    }
    
    .remove-item-btn {
        min-height: 32px !important;
        min-width: 32px !important;
        padding: 0.375rem !important;
        font-size: 0.6875rem !important;
    }
    
    /* Voice and send buttons on phone */
    #voiceBtn,
    #sendBtn {
        min-height: 40px !important;
        min-width: 40px !important;
        padding: 0.625rem !important;
    }
    
    /* Dropdown items on phone */
    .dropdown-menu button,
    .theme-option,
    .discount-option,  
    .customer-quick {
        padding: 0.625rem 0.75rem !important;
        min-height: 40px !important;
        font-size: 0.8125rem !important;
    }
    
    /* Category tabs on phone - even more compact single line */
    .category-tabs-container {
        display: flex !important;
        flex-wrap: nowrap !important;
        align-items: center !important;
        overflow-x: auto !important;
        gap: 0.1875rem !important;
        padding: 0 !important; /* Remove vertical padding to fix alignment */
        margin: 0 !important; /* Remove margin to fix alignment */
        scrollbar-width: none !important;
        -ms-overflow-style: none !important;
        /* Remove fade effect on phone for better visibility */
        mask-image: none !important;
        -webkit-mask-image: none !important;
    }
    
    .category-tabs-container::-webkit-scrollbar {
        display: none !important;
    }
    
    .category-tab {
        padding: 0.1875rem 0.25rem !important;
        font-size: 0.5625rem !important;
        margin: 0 !important;
        min-height: 24px !important;
        height: 24px !important;
        max-height: 24px !important;
        border-radius: 3px !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        white-space: nowrap !important;
        flex-shrink: 0 !important;
        min-width: fit-content !important;
        box-sizing: border-box !important;
        align-self: center !important;
        vertical-align: middle !important;
    }
    
    /* Ensure active state maintains height on phone */
    .category-tab.active {
        height: 24px !important;
        min-height: 24px !important;
        max-height: 24px !important;
    }
    
    .category-tab i {
        font-size: 0.5625rem !important;
        margin-right: 0.125rem !important;
    }
    
    /* Phone scroll buttons - match category tab height exactly */
    .category-scroll-btn {
        height: 24px !important;
        min-height: 24px !important;
        max-height: 24px !important;
        width: 24px !important;
        min-width: 24px !important;
        font-size: 0.5625rem !important;
        border-width: 1px !important; /* Thinner border on small screens */
        margin: 0 !important;
        padding: 0 !important;
        border-radius: 3px !important;
        box-sizing: border-box !important;
        align-self: center !important;
        vertical-align: middle !important;
    }
    
    /* Phone category tabs wrapper */
    .category-tabs-wrapper {
        gap: 0.0625rem !important;
    }
    
    /* Focus states for phone */
    input[type="text"]:focus {
        outline: 2px solid var(--accent-primary) !important;
        outline-offset: 2px !important;
        box-shadow: 0 0 0 3px rgba(var(--accent-primary-rgb), 0.1) !important;
    }
    
    button:focus {
        outline: 2px solid var(--accent-primary) !important;
        outline-offset: 2px !important;
        box-shadow: 0 0 0 3px rgba(var(--accent-primary-rgb), 0.1) !important;
    }
}

/* Universal button and form improvements across all screen sizes */
@media (max-width: 767px) {
    /* Ensure consistent button styling */
    button:active {
        transform: scale(0.98) !important;
        transition: transform 0.1s ease !important;
    }
    
    /* Input field improvements */
    input[type="text"] {
        -webkit-appearance: none !important;
        appearance: none !important;
        background-color: var(--bg-primary) !important;
        border: 1px solid var(--border-color) !important;
        color: var(--text-primary) !important;
    }
    
    input[type="text"]:focus {
        border-color: var(--accent-primary) !important;
        box-shadow: 0 0 0 3px rgba(var(--accent-primary-rgb), 0.1) !important;
        outline: none !important;
    }
    
    /* Button hover states for touch devices */
    @media (hover: none) and (pointer: coarse) {
        button:hover {
            transform: none !important;
        }
        
        button:active {
            opacity: 0.8 !important;
            transform: scale(0.98) !important;
        }
    }
    
    /* Improve readability on small screens */
    ::placeholder {
        color: var(--text-muted) !important;
        opacity: 0.7 !important;
    }
}

/* Mobile-First Quick Actions Strategy */
/* Hide horizontal actions by default (desktop) */
#horizontalActionsBar {
    display: none;
}

/* Mobile Quick Actions - Ensure they're accessible in the order tab */
@media (max-width: 767px) {
    /* Show quick actions when in order tab on mobile */
    .mobile-order-active .quick-actions-compact {
        display: flex !important;
        gap: 0.5rem;
        padding: 0.5rem 0;
    }
    
    /* Hide quick actions in products tab to save space */
    .mobile-products-active .quick-actions-compact {
        display: none !important;
    }
    
    /* Keep horizontal actions bar available as fallback */
    #horizontalActionsBar {
        display: block !important;
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        z-index: 1000;
        padding: 0.75rem;
        background-color: var(--bg-secondary);
        border-top: 1px solid var(--border-color);
        box-shadow: 0 -4px 12px rgba(0, 0, 0, 0.1);
    }
    
    /* Add bottom padding to main content to prevent overlap */
    body {
        padding-bottom: 95px;
    }
    
    /* Ensure main container doesn't get covered */
    .max-w-7xl {
        margin-bottom: 0.5rem;
    }
}

/* Tablet-specific adjustments (iPad landscape, etc.) */
@media (min-width: 768px) and (max-width: 1024px) {
    #horizontalActionsBar {
        padding: 1rem;
    }
    
    body {
        padding-bottom: 100px;
    }
    
    .horizontal-action {
        min-height: 70px;
        padding: 1rem 0.75rem;
    }
    
    .horizontal-action i {
        font-size: 1.5rem;
        margin-bottom: 0.5rem;
    }
    
    .horizontal-action span {
        font-size: 0.875rem;
    }
}

/* Horizontal Quick Actions */
.horizontal-actions {
    display: flex;
    gap: 0.5rem;
    overflow-x: auto;
    padding: 0.5rem 0;
    scrollbar-width: none;
    -ms-overflow-style: none;
    justify-content: space-between;
}

.horizontal-actions::-webkit-scrollbar {
    display: none;
}

.horizontal-action {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 0.75rem 0.5rem;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    background-color: var(--bg-secondary);
    min-width: 60px;
    min-height: 60px; /* Ensure minimum touch target size */
    cursor: pointer;
    transition: all 0.2s ease;
    touch-action: manipulation; /* Improves touch responsiveness */
}

/* Desktop hover effects */
@media (hover: hover) and (pointer: fine) {
    .horizontal-action:hover {
        background-color: var(--bg-accent);
        border-color: var(--accent-primary);
        transform: translateY(-1px);
    }
}

/* Touch device effects */
@media (hover: none) and (pointer: coarse) {
    .horizontal-action:active {
        background-color: var(--bg-accent);
        border-color: var(--accent-primary);
        transform: scale(0.98);
    }
}

.horizontal-action i {
    font-size: 1.25rem;
    color: var(--accent-primary);
    margin-bottom: 0.25rem;
}

.horizontal-action span {
    font-size: 0.7rem;
    color: var(--text-secondary);
    text-align: center;
    line-height: 1.1;
    font-weight: 500;
    white-space: nowrap;
}

/* Mobile-specific enhancements */
@media (max-width: 768px) {
    .horizontal-action {
        min-height: 60px !important;
        padding: 0.75rem 0.5rem !important;
        font-size: 0.8125rem !important;
    }
    
    .horizontal-action i {
        font-size: 1.25rem !important;
        margin-bottom: 0.25rem !important;
    }
    
    .horizontal-action span {
        font-size: 0.6875rem !important;
        font-weight: 500 !important;
    }
}

@media (max-width: 480px) {
    .horizontal-action {
        min-height: 56px !important;
        padding: 0.625rem 0.25rem !important;
        font-size: 0.75rem !important;
    }
    
    .horizontal-action i {
        font-size: 1.125rem !important;
        margin-bottom: 0.125rem !important;
    }
    
    .horizontal-action span {
        font-size: 0.625rem !important;
        font-weight: 500 !important;
        line-height: 1 !important;
    }
}

/* Compact Order Summary */
.compact-summary {
    background-color: var(--bg-accent);
    border-radius: var(--border-radius);
    padding: 0.75rem;
    margin-bottom: 1rem;
}

.summary-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.25rem;
}

.summary-row:last-child {
    margin-bottom: 0;
    padding-top: 0.5rem;
    border-top: 1px solid var(--border-color);
    font-weight: 600;
}

/* Enhanced Order Item Cards */
.order-item-card {
    background-color: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 12px;
    margin-bottom: 8px;
    transition: all 0.2s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    min-height: 100px; /* Default mode: ~100-120px total */
}

/* Compact mode styling */
.order-items-list[style*="250px"] .order-item-card {
    padding: 8px 12px;
    margin-bottom: 6px;
    min-height: 80px; /* Compact mode: ~80-100px total */
    border-radius: 6px;
}

.order-item-card:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    border-color: var(--accent-primary);
}

.order-item-header {
    display: flex;
    align-items: flex-start;
    margin-bottom: 8px;
    gap: 12px;
}

/* Compact mode header */
.order-items-list[style*="250px"] .order-item-header {
    margin-bottom: 6px;
    gap: 8px;
}

.product-icon {
    width: 32px;
    height: 32px;
    border-radius: 8px;
    background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary));
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 12px;
    color: white;
    font-size: 14px;
    flex-shrink: 0;
}

/* Compact mode icon */
.order-items-list[style*="250px"] .product-icon {
    width: 28px;
    height: 28px;
    border-radius: 6px;
    font-size: 12px;
    margin-right: 8px;
}

.product-info {
    flex: 1;
    min-width: 0;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.product-name {
    font-weight: 600;
    font-size: 0.95rem;
    color: var(--text-primary);
    margin-bottom: 3px;
    line-height: 1.2;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* Compact mode product name */
.order-items-list[style*="250px"] .product-name {
    font-size: 0.875rem;
    line-height: 1.1;
    margin-bottom: 2px;
}

.product-unit-price {
    font-size: 0.8rem;
    color: var(--text-muted);
    line-height: 1;
}

/* Compact mode unit price */
.order-items-list[style*="250px"] .product-unit-price {
    font-size: 0.75rem;
}

.quantity-controls {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-top: 8px;
}

/* Compact mode quantity controls */
.order-items-list[style*="250px"] .quantity-controls {
    gap: 8px;
    margin-top: 6px;
}

.quantity-btn {
    width: 28px;
    height: 28px;
    border-radius: 50%;
    border: none;
    background-color: var(--bg-accent);
    color: var(--text-secondary);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 11px;
}

/* Compact mode quantity buttons */
.order-items-list[style*="250px"] .quantity-btn {
    width: 24px;
    height: 24px;
    font-size: 10px;
}

.quantity-btn:hover {
    background-color: var(--accent-primary);
    color: white;
    transform: scale(1.1);
}

.quantity-btn:active {
    transform: scale(0.95);
}

.quantity-display {
    min-width: 36px;
    text-align: center;
    font-weight: 600;
    font-size: 0.95rem;
    color: var(--text-primary);
}

/* Compact mode quantity display */
.order-items-list[style*="250px"] .quantity-display {
    min-width: 32px;
    font-size: 0.875rem;
}

.item-total-price {
    font-weight: 700;
    font-size: 1rem;
    color: var(--text-primary);
    margin-left: auto;
    text-align: right;
    min-width: 60px;
    flex-shrink: 0;
}

/* Compact mode item total price */
.order-items-list[style*="250px"] .item-total-price {
    font-size: 0.9rem;
    min-width: 55px;
}

.remove-item-btn {
    width: 26px;
    height: 26px;
    border-radius: 6px;
    border: none;
    background-color: rgba(239, 68, 68, 0.1);
    color: #dc2626;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    margin-left: 8px;
    font-size: 11px;
    flex-shrink: 0;
}

/* Compact mode remove button */
.order-items-list[style*="250px"] .remove-item-btn {
    width: 22px;
    height: 22px;
    font-size: 10px;
    border-radius: 4px;
    margin-left: 6px;
}

.remove-item-btn:hover {
    background-color: rgba(239, 68, 68, 0.2);
    transform: scale(1.1);
}

.empty-order-state {
    text-align: center;
    padding: 48px 24px;
    color: var(--text-muted);
}

.empty-order-icon {
    font-size: 48px;
    opacity: 0.5;
    margin-bottom: 16px;
}

.empty-order-text {
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 8px;
}

.empty-order-subtext {
    font-size: 14px;
    opacity: 0.8;
}

/* Product icon color variations */
.product-icon.coffee { background: linear-gradient(135deg, #8B4513, #A0522D); }
.product-icon.latte { background: linear-gradient(135deg, #D2691E, #F4A460); }
.product-icon.croissant { background: linear-gradient(135deg, #DAA520, #FFD700); }
.product-icon.muffin { background: linear-gradient(135deg, #CD853F, #DEB887); }
.product-icon.sandwich { background: linear-gradient(135deg, #228B22, #32CD32); }
.product-icon.juice { background: linear-gradient(135deg, #FF6347, #FF7F50); }

/* Animation for adding/removing items */
@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideOutDown {
    from {
        opacity: 1;
        transform: translateY(0);
    }
    to {
        opacity: 0;
        transform: translateY(20px);
    }
}

.order-item-card.adding {
    animation: slideInUp 0.3s ease-out;
}

.order-item-card.removing {
    animation: slideOutDown 0.3s ease-out;
}

/* Quantity change pulse effect */
.quantity-pulse {
    animation: pulse 0.4s ease-in-out;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}
    